<script setup lang="ts">
  import type { TableColumn } from '../data';

  import { computed, ref } from 'vue';

  interface Props {
    // 新增：当前激活的标签页
    activeTab?: string;
    // 新增：银行回单统计信息
    bankSummary?: {
      expenseAmount: number;
      expenseCount: number;
      incomeAmount: number;
      incomeCount: number;
    };
    columns: TableColumn[];
    dataSource: any[];
    loading?: boolean;
    onResizeColumn?: (width: number, column: any) => void;
    pagination?: {
      current: number;
      pageSize: number;
      total: number;
    };
    rowSelection?: any;
    tableKey?: string;
  }

  interface Emits {
    (e: 'paginationChange', page: number, pageSize: number): void;
    (e: 'pageSizeChange', current: number, size: number): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    activeTab: '',
    bankSummary: undefined,
    loading: false,
    onResizeColumn: undefined,
    pagination: undefined,
    rowSelection: undefined,
    tableKey: 'data-table',
  });

  const emit = defineEmits<Emits>();

  // 表格引用
  const tableRef = ref();

  // 表格滚动配置
  const tableScrollConfig = computed(() => ({
    x: '100%', // 关键：使用固定值而不是 max-content，让列宽配置生效
    y: 'calc(100vh - 336px)', // 设置垂直滚动高度，为横向滚动条预留空间
  }));

  // 分页后的表格数据
  const paginatedTableData = computed(() => {
    if (!props.pagination) return props.dataSource;

    const start = (props.pagination.current - 1) * props.pagination.pageSize;
    const end = start + props.pagination.pageSize;
    return props.dataSource.slice(start, end);
  });

  // 处理列宽调整
  const handleResizeColumn = (w: number, col: any) => {
    // 必须更新列宽，否则拖拽不会生效
    col.width = w;

    // 调用父组件的回调
    if (props.onResizeColumn) {
      props.onResizeColumn(w, col);
    }
  };

  // 分页处理方法
  const handlePaginationChange = (page: number, pageSize: number) => {
    emit('paginationChange', page, pageSize);
  };

  const handlePageSizeChange = (current: number, size: number) => {
    emit('pageSizeChange', current, size);
  };

  // 格式化金额，保留两位小数并添加千分位分隔符
  const formatAmount = (amount: number): string => {
    if (amount === undefined || amount === null) {
      return '0.00';
    }
    return amount.toLocaleString('zh-CN', {
      maximumFractionDigits: 2,
      minimumFractionDigits: 2,
    });
  };
</script>

<template>
  <div class="data-table-container">
    <!-- 表格内容区域 -->
    <div class="data-table-wrap">
      <a-table
        ref="tableRef"
        :key="tableKey"
        :columns="columns"
        :data-source="paginatedTableData"
        :loading="loading"
        :pagination="false"
        :row-selection="rowSelection"
        :scroll="tableScrollConfig"
        row-key="_id"
        size="small"
        table-layout="fixed"
        @resize-column="handleResizeColumn"
      />
    </div>

    <!-- 银行回单统计信息 -->
    <div v-if="activeTab === 'bank' && bankSummary" class="bank-summary">
      <div class="summary-item income">
        <span class="summary-label">
          收入金额合计({{ bankSummary.incomeCount }}条)：
        </span>
        <span class="summary-amount income-amount">
          {{ formatAmount(bankSummary.incomeAmount) }}
        </span>
      </div>
      <div class="summary-item expense">
        <span class="summary-label">
          支出金额合计({{ bankSummary.expenseCount }}条)：
        </span>
        <span class="summary-amount expense-amount">
          {{ formatAmount(bankSummary.expenseAmount) }}
        </span>
      </div>
    </div>

    <!-- 底部分页区域 -->
    <div v-if="pagination" class="data-table-footer">
      <div class="pagination-wrap">
        <div class="pagination-info">
          第
          {{
            pagination.total > 0
              ? (pagination.current - 1) * pagination.pageSize + 1
              : 0
          }}
          到
          {{
            Math.min(pagination.current * pagination.pageSize, pagination.total)
          }}
          条，总计 {{ pagination.total }} 条
        </div>
        <a-pagination
          :current="pagination.current"
          :page-size="pagination.pageSize"
          :total="pagination.total"
          :show-size-changer="true"
          :show-quick-jumper="true"
          :page-size-options="['20', '50', '100']"
          size="small"
          @change="handlePaginationChange"
          @show-size-change="handlePageSizeChange"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
  /* 表格容器样式 */
  .data-table-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
    background: #fff;
  }

  .data-table-wrap {
    flex: 1;
    height: calc(100vh - 320px);
    min-height: 400px;
    overflow: hidden;
    background: #fff;
  }

  /* 底部分页区域样式 */
  .data-table-footer {
    flex-shrink: 0;
    padding: 8px 16px;
    background: #fff;
    border-top: 1px solid #f0f0f0;
  }

  .pagination-wrap {
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 40px;
    padding: 4px 0;
  }

  .pagination-info {
    font-size: 12px;
    color: #666;
  }

  /* 银行回单统计信息样式 */
  .bank-summary {
    display: flex;
    flex-shrink: 0;
    gap: 32px;
    align-items: center;
    justify-content: center;
    padding: 12px 16px;
    background: linear-gradient(135deg, #f8f9fa 0%, #f1f3f4 100%);
    border-top: 1px solid #e8eaed;
    border-bottom: 1px solid #e8eaed;
  }

  .summary-item {
    display: flex;
    gap: 8px;
    align-items: center;
  }

  .summary-label {
    font-size: 13px;
    font-weight: 500;
    color: #333;
  }

  .summary-amount {
    font-family: Consolas, Monaco, 'Courier New', monospace;
    font-size: 14px;
    font-weight: 600;
  }

  .income-amount {
    color: #52c41a;
  }

  .expense-amount {
    color: #ff4d4f;
  }

  /* 表格样式 */
  :deep(.ant-table) {
    overflow: hidden;
    font-size: 12px;
    border-radius: 0;

    .ant-table-thead > tr > th {
      box-sizing: border-box;
      height: 32px;
      padding: 6px 8px !important;
      font-size: 12px;
      font-weight: 600;
      color: #333;
      white-space: nowrap;
      background: linear-gradient(135deg, #f8f9fa 0%, #f1f3f4 100%);
      border-bottom: 2px solid #e8eaed;
    }

    .ant-table-tbody > tr > td {
      box-sizing: border-box;

      /* 移除全局max-width限制，让列配置中的宽度设置生效 */
      height: 30px;
      min-height: 30px;
      max-height: 30px;
      padding: 4px 8px;
      font-size: 12px;
      line-height: 22px;
      border-bottom: 1px solid #f0f0f0;

      /* 确保单元格内容不会超出 */
      > * {
        max-width: 100%;
      }
    }

    .ant-table-tbody > tr:hover > td {
      background-color: #f8f9ff;
    }

    .ant-table-tbody > tr {
      height: 30px;
    }
  }

  :deep(.ant-pagination-options) {
    margin-left: 8px !important;
  }

  /* 表格单元格文本样式 */
  :deep(.table-cell-text) {
    display: block;
    width: 100%;
    line-height: 22px;
  }

  :deep(.table-cell-ellipsis) {
    display: block;
    width: 100%;
    overflow: hidden;
    line-height: 22px;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: default;
  }

  /* 确保表格单元格支持省略号 */
  :deep(.ant-table-tbody > tr > td) {
    /* 为所有单元格设置省略号支持 */
    .ant-table-cell-ellipsis {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
</style>
