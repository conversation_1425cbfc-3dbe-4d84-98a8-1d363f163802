import { ref, computed, watch } from 'vue';
import { message } from 'ant-design-vue';
import type {
  BankReceiptData,
  BankReceiptQueryParams,
  InvoiceData,
  InvoiceQueryParams,
  PayrollData,
  PayrollQueryParams,
} from '#/api/jsj-ai/types';
import {
  fetchBankReceiptList,
  fetchInvoiceList,
  fetchPayrollList,
} from '#/api/jsj-ai/api-v2';

// 扩展的发票数据类型，用于货物维度展示
export interface ExpandedInvoiceData extends InvoiceData {
  detail_id?: number; // 明细ID，用于区分同一发票的不同明细
  detail_goods_name?: string; // 明细货物名称
  detail_specification?: string; // 明细规格型号
  detail_unit?: string; // 明细单位
  detail_quantity?: number; // 明细数量
  detail_unit_price?: number; // 明细单价
  detail_amount?: number; // 明细金额
  detail_tax_rate?: string; // 明细税率
  detail_tax_amount?: number; // 明细税额
  detail_total?: number; // 明细价税合计
  detail_scene?: string; // 明细AI记账场景
  is_detail_row?: boolean; // 标识是否为明细行
}

export function useOriginalVoucherData() {
  // 数据源
  const tableData = ref<(BankReceiptData | InvoiceData | PayrollData | ExpandedInvoiceData)[]>([]);
  const originalBankData = ref<BankReceiptData[]>([]); // 存储银行回单原始数据，用于前端筛选
  const originalInvoiceData = ref<InvoiceData[]>([]); // 存储发票原始数据，用于前端搜索
  const originalPayrollData = ref<PayrollData[]>([]); // 存储工资单原始数据，用于前端筛选
  const loading = ref(false);

  // 展示维度状态
  const displayDimension = ref<'invoice' | 'goods'>('invoice');

  // 前端搜索关键字
  const searchKeyword = ref('');

  // 分页配置
  const pagination = ref({
    current: 1,
    pageSize: 20,
    total: 0,
  });

  /**
   * 将发票数据展开为货物维度数据
   * @param invoices 原始发票数据
   * @returns 展开后的数据
   */
  const expandInvoiceToGoodsDimension = (invoices: InvoiceData[]): ExpandedInvoiceData[] => {
    const expandedData: ExpandedInvoiceData[] = [];

    invoices.forEach((invoice) => {
      if (invoice.details && invoice.details.length > 0) {
        // 有明细数据，为每个明细创建一行
        invoice.details.forEach((detail) => {
          expandedData.push({
            ...invoice,
            detail_id: detail.id,
            detail_goods_name: detail.goods_name,
            detail_specification: detail.specification,
            detail_unit: detail.unit,
            detail_quantity: detail.quantity,
            detail_unit_price: detail.unit_price,
            detail_amount: detail.amount,
            detail_tax_rate: detail.tax_rate,
            detail_tax_amount: detail.tax_amount,
            detail_total: detail.total,
            detail_scene: detail.scene,
            is_detail_row: true,
            // 为了区分不同明细行，使用发票ID + 明细ID作为唯一标识
            _id: `${invoice._id}_detail_${detail.id}`,
          });
        });
      } else {
        // 没有明细数据，保持原发票数据
        expandedData.push({
          ...invoice,
          is_detail_row: false,
        });
      }
    });

    return expandedData;
  };

  /**
   * 根据展示维度处理数据
   * @param invoices 原始发票数据
   * @param dimension 展示维度
   * @returns 处理后的数据
   */
  const processInvoiceDataByDimension = (
    invoices: InvoiceData[],
    dimension: 'invoice' | 'goods'
  ): (InvoiceData | ExpandedInvoiceData)[] => {
    if (dimension === 'goods') {
      return expandInvoiceToGoodsDimension(invoices);
    }
    return invoices;
  };

  // 前端搜索过滤后的数据
  const filteredTableData = computed(() => {
    if (!searchKeyword.value.trim()) {
      return tableData.value;
    }

    const keyword = searchKeyword.value.toLowerCase().trim();

    return tableData.value.filter((item) => {
      // 检查是否是发票数据（通过检查是否有发票相关字段）
      const hasInvoiceFields = 'digital_invoice_number' in item ||
                              'invoice_number' in item ||
                              'goods_name' in item ||
                              'seller_name' in item ||
                              'buyer_name' in item;

      if (hasInvoiceFields) {
        // 将对象转换为any类型以便访问所有可能的字段
        const invoice = item as any;

        // 搜索所有可能的字段，包括不同的命名方式
        const searchFields = [
          // 基本信息
          invoice.seller_name || '',
          invoice.buyer_name || '',
          invoice.goods_name || '',
          invoice.digital_invoice_number || '',
          invoice.voucher_num || '',
          invoice.remark || '',

          // 金额相关
          invoice.total_amount?.toString() || '',
          invoice.total_tax?.toString() || '',
          invoice.total?.toString() || '',

          // 其他可能的字段
          invoice.type || '',
          invoice.scene || '',
          invoice.status || '',
        ];

        return searchFields.some(field =>
          field && field.toString().toLowerCase().includes(keyword)
        );
      }

      // 对于非发票数据，返回原数据（银行回单、工资单等）
      return true;
    });
  });

  // 选中状态
  const selectedRowKeys = ref<string[]>([]);
  const selectedInvoices = ref<any[]>([]); // 支持多种数据类型：发票、银行回单、工资单

  // 表格行选择配置
  const rowSelection = computed(() => ({
    onChange: (keys: any[], rows: any[]) => {
      selectedRowKeys.value = keys.map(String);
      selectedInvoices.value = rows;
    },
    onSelectAll: (
      selected: boolean,
      selectedRows: any[],
      changeRows: any[],
    ) => {
      console.log('全选变化:', selected, selectedRows, changeRows);
    },
    selectedRowKeys: selectedRowKeys.value,
    type: 'checkbox' as const,
  }));

  // 通用记账状态筛选函数
  const applyAccountingStatusFilter = (data: any[], accountingStatus: string) => {
    if (!accountingStatus || accountingStatus === '') {
      return data; // 全部，不筛选
    }

    if (accountingStatus === 'accounted') {
      // 已记账：voucher_id不为空
      return data.filter(
        (item) => item.voucher_id && item.voucher_id.trim() !== '',
      );
    } else if (accountingStatus === 'unaccounted') {
      // 未记账：voucher_id为空或不存在
      return data.filter(
        (item) => !item.voucher_id || item.voucher_id.trim() === '',
      );
    }

    return data;
  };

  // 前端筛选银行回单数据
  const filterBankReceiptData = (formData: Record<string, any>) => {
    let filteredData = [...originalBankData.value];

    // 银行账号筛选（使用account_number）
    if (formData.account_number && formData.account_number !== '') {
      filteredData = filteredData.filter(
        (item) => item.account_number === formData.account_number,
      );
    }

    // 搜索文本筛选（摘要/户名/备注）
    if (formData.search_text && formData.search_text.trim() !== '') {
      const searchText = formData.search_text.trim().toLowerCase();
      filteredData = filteredData.filter(
        (item) =>
          (item.summary && item.summary.toLowerCase().includes(searchText)) ||
          (item.counterparty_account_name &&
            item.counterparty_account_name
              .toLowerCase()
              .includes(searchText)) ||
          (item.note && item.note.toLowerCase().includes(searchText)),
      );
    }

    // 记账状态筛选
    filteredData = applyAccountingStatusFilter(filteredData, formData.accounting_status);

    tableData.value = filteredData;
    pagination.value.total = filteredData.length;
    
    // 如果当前页超出范围，重置到第一页
    if (
      pagination.value.current >
      Math.ceil(filteredData.length / pagination.value.pageSize)
    ) {
      pagination.value.current = 1;
    }
  };

  // 前端筛选发票数据
  const filterInvoiceData = (formData: Record<string, any>) => {
    let filteredData = [...originalInvoiceData.value];

    // 记账状态筛选
    filteredData = applyAccountingStatusFilter(filteredData, formData.accounting_status);

    // 根据展示维度处理筛选后的数据
    const processedData = processInvoiceDataByDimension(filteredData, displayDimension.value);
    tableData.value = processedData;
    pagination.value.total = processedData.length;

    // 如果当前页超出范围，重置到第一页
    if (
      pagination.value.current >
      Math.ceil(processedData.length / pagination.value.pageSize)
    ) {
      pagination.value.current = 1;
    }
  };

  // 前端筛选工资单数据
  const filterPayrollData = (formData: Record<string, any>) => {
    let filteredData = [...originalPayrollData.value];

    // 记账状态筛选
    filteredData = applyAccountingStatusFilter(filteredData, formData.accounting_status);

    tableData.value = filteredData;
    pagination.value.total = filteredData.length;

    // 如果当前页超出范围，重置到第一页
    if (
      pagination.value.current >
      Math.ceil(filteredData.length / pagination.value.pageSize)
    ) {
      pagination.value.current = 1;
    }
  };

  // 查询银行回单数据
  const fetchBankReceiptData = async (params: BankReceiptQueryParams) => {
    loading.value = true;
    try {
      // 过滤掉空值参数
      Object.keys(params).forEach((key) => {
        const value = params[key as keyof BankReceiptQueryParams];
        if (value === undefined || value === null || value === '') {
          delete params[key as keyof BankReceiptQueryParams];
        }
      });

      const result = await fetchBankReceiptList(params);

      // 保存原始数据
      originalBankData.value = result || [];
      console.log('获取银行回单数据成功:', result?.length || 0, '条记录');

      // 应用前端筛选
      filterBankReceiptData({});

      return true;
    } catch (error: any) {
      console.error('银行回单查询失败:', error);
      message.error(error?.message || '银行回单查询失败');
      originalBankData.value = [];
      tableData.value = [];
      pagination.value.total = 0;
      return false;
    } finally {
      loading.value = false;
    }
  };

  // 查询工资单数据
  const fetchPayrollData = async (params: PayrollQueryParams) => {
    loading.value = true;
    try {
      // 过滤掉空值参数
      Object.keys(params).forEach((key) => {
        const value = params[key as keyof PayrollQueryParams];
        if (value === undefined || value === null || value === '') {
          delete params[key as keyof PayrollQueryParams];
        }
      });

      const result = await fetchPayrollList(params);

      tableData.value = result || [];
      originalPayrollData.value = result || []; // 保存原始工资单数据
      console.log('获取工资单数据成功:', result?.length || 0, '条记录');

      // 更新分页信息
      pagination.value.total = tableData.value.length;
      if (
        pagination.value.current >
        Math.ceil(tableData.value.length / pagination.value.pageSize)
      ) {
        pagination.value.current = 1;
      }

      return true;
    } catch (error: any) {
      console.error('工资单查询失败:', error);
      message.error(error?.message || '工资单查询失败');
      tableData.value = [];
      pagination.value.total = 0;
      return false;
    } finally {
      loading.value = false;
    }
  };

  // 查询发票数据
  const fetchInvoiceData = async (params: InvoiceQueryParams) => {
    loading.value = true;
    try {
      // 过滤掉空值参数
      Object.keys(params).forEach((key) => {
        const value = params[key as keyof InvoiceQueryParams];
        if (value === undefined || value === null || value === '') {
          delete params[key as keyof InvoiceQueryParams];
        }
      });

      const result = await fetchInvoiceList(params);
      originalInvoiceData.value = result || []; // 保存原始发票数据
      // 根据展示维度处理数据
      const processedData = processInvoiceDataByDimension(result || [], displayDimension.value);
      tableData.value = processedData;

      console.log('获取发票数据成功:', result?.length || 0, '条记录');
      console.log('展示维度:', displayDimension.value, '处理后数据:', processedData.length, '条');

      // 更新分页信息
      pagination.value.total = filteredTableData.value.length;
      if (
        pagination.value.current >
        Math.ceil(filteredTableData.value.length / pagination.value.pageSize)
      ) {
        pagination.value.current = 1;
      }

      return true;
    } catch (error: any) {
      console.error('发票查询失败:', error);
      message.error(error?.message || '发票查询失败');
      tableData.value = [];
      pagination.value.total = 0;
      return false;
    } finally {
      loading.value = false;
    }
  };

  // 清空选中状态
  const clearSelectedInvoices = () => {
    selectedInvoices.value = [];
    selectedRowKeys.value = [];
  };

  // 分页处理方法
  const handlePaginationChange = (page: number, pageSize: number) => {
    pagination.value.current = page;
    pagination.value.pageSize = pageSize;
  };

  const handlePageSizeChange = (_current: number, size: number) => {
    pagination.value.current = 1; // 重置到第一页
    pagination.value.pageSize = size;
  };

  // 搜索功能
  const handleSearch = (keyword: string) => {
    searchKeyword.value = keyword;
    // 重置分页到第一页
    pagination.value.current = 1;
    // 更新分页总数
    pagination.value.total = filteredTableData.value.length;
  };

  // 清空搜索
  const clearSearch = () => {
    searchKeyword.value = '';
    pagination.value.current = 1;
    pagination.value.total = tableData.value.length;
  };

  // 监听表格数据变化，清空选中状态
  watch(tableData, () => {
    clearSelectedInvoices();
  });

  // 监听搜索关键字变化，更新分页
  watch(searchKeyword, () => {
    pagination.value.total = filteredTableData.value.length;
    if (pagination.value.current > Math.ceil(filteredTableData.value.length / pagination.value.pageSize)) {
      pagination.value.current = 1;
    }
  });

  // 更新展示维度
  const updateDisplayDimension = (dimension: 'invoice' | 'goods') => {
    displayDimension.value = dimension;

    // 如果有原始发票数据，重新处理数据
    if (originalInvoiceData.value.length > 0) {
      // 重新应用当前的筛选条件，确保维度切换时保持筛选状态
      // 这里需要重新调用 filterInvoiceData，但需要传入当前的筛选条件
      // 为了简化，我们直接重新处理原始数据，后续可以通过外部调用 filterInvoiceData 来应用筛选
      const processedData = processInvoiceDataByDimension(originalInvoiceData.value, dimension);
      tableData.value = processedData;

      // 重新计算分页
      pagination.value.total = filteredTableData.value.length;
      pagination.value.current = 1; // 重置到第一页

      console.log('切换展示维度:', dimension, '处理后数据:', processedData.length, '条');
    }
  };

  return {
    // 数据
    tableData: filteredTableData, // 返回过滤后的数据
    originalBankData,
    loading,
    pagination,
    selectedRowKeys,
    selectedInvoices,
    rowSelection,
    searchKeyword,
    displayDimension,

    // 方法
    filterBankReceiptData,
    filterInvoiceData,
    filterPayrollData,
    fetchBankReceiptData,
    fetchPayrollData,
    fetchInvoiceData,
    clearSelectedInvoices,
    handlePaginationChange,
    handlePageSizeChange,
    handleSearch,
    clearSearch,
    updateDisplayDimension,
  };
}
